# Technical Specification: Application Letter Template Migration

## Executive Summary

This document outlines the technical specification for migrating the application letter generation system from AI-based HTML generation to a programmatic template-based approach, following the architecture pattern established by the resume builder.

**Current State**: AI generates plain text, then OpenAI fills HTML templates dynamically
**Target State**: AI generates structured data, Handlebars templates render HTML programmatically

## 1. Current Application Letter Architecture Analysis

### 1.1 Generation Flow
```mermaid
graph TD
    A[User Input] --> B[Resume + Job Info]
    B --> C[Gemini AI]
    C --> D[Plain Text Letter]
    D --> E[OpenAI API]
    E --> F[HTML Output]
    F --> G[PDF Generation]
```

### 1.2 Key Components
- **Frontend**: `/app/application-letter/page.tsx`
  - 4-step wizard interface
  - Real-time streaming support
  - Template selection in Step 3
  
- **Backend API**: `/app/api/generate-application-letter/route.ts`
  - Handles file processing
  - Manages streaming responses
  - Token validation

- **Edge Functions**:
  - `generate-letter-unified.ts`: Unified streaming approach
  - Two-phase generation: Gemini for text, OpenAI for HTML

- **Templates**: Currently stored as full HTML strings with inline CSS

### 1.3 Pain Points
1. **Cost**: Each generation requires two AI API calls (Gemini + OpenAI)
2. **Latency**: OpenAI template filling adds 3-5 seconds
3. **Reliability**: AI HTML generation can produce inconsistent results
4. **Maintenance**: HTML templates are difficult to modify
5. **Testing**: Cannot test templates without AI generation

## 2. Proposed Architecture

### 2.1 New Generation Flow
```mermaid
graph TD
    A[User Input] --> B[Resume + Job Info]
    B --> C[Gemini AI]
    C --> D[Structured Letter Data]
    D --> E[Handlebars Engine]
    E --> F[HTML Output]
    F --> G[PDF Generation]
```

### 2.2 Benefits
- **Cost Reduction**: Eliminate OpenAI API calls (save ~$0.02 per generation)
- **Performance**: Reduce generation time by 3-5 seconds
- **Reliability**: Consistent template rendering
- **Maintainability**: Easier template modifications
- **Testability**: Templates can be tested independently

## 3. Data Structure Design

### 3.1 Structured Letter Data Format

```typescript
interface StructuredLetterData {
  // Letter metadata
  metadata: {
    generatedAt: string;
    lastModified: string;
    templateId: string;
    language: 'id' | 'en';
  };
  
  // Header information
  header: {
    date: string;           // "27 Mei 2025"
    formattedDate?: string; // For alternative formats
  };
  
  // Subject line
  subject: {
    prefix: string;         // "Perihal: Lamaran Pekerjaan sebagai"
    position: string;       // "Fullstack Developer"
  };
  
  // Recipient
  recipient: {
    salutation: string;     // "Yth."
    title: string;          // "Bapak/Ibu Bagian Sumber Daya Manusia"
    company?: string;       // "PT Example Company"
    address?: string[];     // ["Jl. Example No. 123", "Jakarta 12345"]
  };
  
  // Letter body
  body: {
    opening: string;        // "Dengan hormat,"
    paragraphs: string[];   // Array of paragraph texts
    closing: string;        // "Atas perhatian dan waktu yang Bapak/Ibu berikan..."
  };
  
  // Signature
  signature: {
    farewell: string;       // "Hormat saya,"
    name: string;           // "John Doe"
    additionalInfo?: string; // Phone, email, etc.
  };
  
  // Optional attachments mention
  attachments?: string[];   // ["Curriculum Vitae", "Portofolio"]
}
```

### 3.2 Template Data Interface

```typescript
interface LetterTemplateData {
  // Formatted date
  date: string;
  
  // Subject line
  subject: string;
  
  // Recipient block
  recipientLines: string[];
  
  // Salutation
  salutation: string;
  
  // Body content
  opening: string;
  paragraphs: string[];
  closing: string;
  
  // Signature block
  farewell: string;
  signatureName: string;
  
  // Styling classes (for template variations)
  styleClasses?: {
    header?: string;
    body?: string;
    signature?: string;
  };
}
```

## 4. Migration Plan

### 4.1 Template Conversion Strategy

#### Phase 1: Extract and Convert Templates
1. Extract HTML structure from existing templates
2. Identify dynamic content areas
3. Convert to Handlebars syntax
4. Create separate CSS files for each template

#### Example Conversion:
```handlebars
<!-- From: Inline HTML -->
<div class="date">
  <p>26 Mei 2025</p>
</div>

<!-- To: Handlebars Template -->
<div class="date">
  <p>{{date}}</p>
</div>
```

#### Phase 2: Create Handlebars Templates
```handlebars
<!-- clean-professional.hbs -->
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <link href="https://fonts.googleapis.com/css2?family={{fontFamily}}" rel="stylesheet">
    <style>{{{styles}}}</style>
</head>
<body>
    <div class="letter-container">
        <div class="header">
            <div class="date">{{date}}</div>
        </div>
        
        <div class="subject">
            <p>{{subject}}</p>
        </div>
        
        <div class="recipient">
            {{#each recipientLines}}
            <p>{{this}}</p>
            {{/each}}
        </div>
        
        <div class="body">
            <p class="salutation">{{salutation}}</p>
            <p class="opening">{{opening}}</p>
            
            {{#each paragraphs}}
            <p class="paragraph">{{this}}</p>
            {{/each}}
            
            <p class="closing">{{closing}}</p>
        </div>
        
        <div class="signature">
            <p class="farewell">{{farewell}}</p>
            <div class="signature-space"></div>
            <p class="name">{{signatureName}}</p>
        </div>
    </div>
</body>
</html>
```

### 4.2 AI Prompt Modification

Update Gemini prompt to generate structured JSON instead of plain text:

```javascript
const structuredPrompt = `
Generate a job application letter in JSON format with the following structure:
{
  "metadata": { ... },
  "header": { ... },
  "subject": { ... },
  "recipient": { ... },
  "body": {
    "opening": "Dengan hormat,",
    "paragraphs": [
      "First paragraph...",
      "Second paragraph...",
      // ... more paragraphs
    ],
    "closing": "Closing paragraph..."
  },
  "signature": { ... }
}

Requirements:
- Total character count: 1300-1600 characters
- Language: Bahasa Indonesia
- Formal business letter style
- Include all standard letter components
`;
```

## 5. Implementation Changes

### 5.1 Backend API Changes

#### New Endpoint Structure
```typescript
// /api/letters/generate-structured/route.ts
export async function POST(request: Request) {
  // 1. Process inputs (resume, job info)
  // 2. Call edge function for structured data generation
  // 3. Return generation ID
  return Response.json({ id: generationId });
}

// /api/letters/[id]/route.ts
export async function GET(request: Request, { params }) {
  // 1. Fetch letter data from DB
  // 2. If structured_data exists, render template
  // 3. Return status and rendered HTML
  return Response.json({ 
    status: letter.status,
    html: renderedHtml,
    structuredData: letter.structured_data
  });
}
```

#### Database Schema Update
```sql
-- Add structured_data column to letters table
ALTER TABLE letters 
ADD COLUMN structured_data JSONB;

-- Add index for performance
CREATE INDEX idx_letters_structured_data ON letters USING gin(structured_data);
```

### 5.2 Edge Function Modifications

```typescript
// generate-letter-structured.ts
serve(async (req) => {
  try {
    // 1. Parse request
    const { resumeData, jobData, templateId } = await req.json();
    
    // 2. Generate structured data with Gemini
    const structuredData = await generateStructuredLetter(
      resumeData,
      jobData,
      geminiApiKey
    );
    
    // 3. Update database
    await supabase
      .from('letters')
      .update({
        structured_data: structuredData,
        status: 'done'
      })
      .eq('id', letterId);
    
    return new Response(JSON.stringify({ success: true }));
  } catch (error) {
    // Error handling
  }
});
```

### 5.3 Frontend Changes

#### Component Updates
```typescript
// hooks/useLetterGeneration.ts
export function useLetterGeneration(generationId: string) {
  const [status, setStatus] = useState('idle');
  const [structuredData, setStructuredData] = useState(null);
  const [htmlContent, setHtmlContent] = useState('');
  
  useEffect(() => {
    // Poll for generation status
    const interval = setInterval(async () => {
      const response = await fetch(`/api/letters/${generationId}`);
      const data = await response.json();
      
      if (data.status === 'done') {
        setStructuredData(data.structuredData);
        setHtmlContent(data.html);
        clearInterval(interval);
      }
    }, 1000);
    
    return () => clearInterval(interval);
  }, [generationId]);
  
  return { status, structuredData, htmlContent };
}
```

#### Live Preview Implementation
```typescript
// components/LetterEditForm.tsx
export function LetterEditForm({ 
  data, 
  onChange,
  selectedTemplate 
}: LetterEditFormProps) {
  const handleParagraphEdit = (index: number, value: string) => {
    const updated = { ...data };
    updated.body.paragraphs[index] = value;
    onChange(updated);
  };
  
  return (
    <div className="space-y-4">
      {/* Date editor */}
      {/* Subject editor */}
      {/* Recipient editor */}
      {/* Paragraphs editor */}
      {/* Signature editor */}
    </div>
  );
}
```

### 5.4 Template Engine Setup

```typescript
// utils/letter-template-engine.ts
import * as Handlebars from 'handlebars';

// Import precompiled templates
import cleanProfessionalTemplate from './handlebars-templates/letters/clean-professional.hbs';
import classicBlueTemplate from './handlebars-templates/letters/classic-blue.hbs';
// ... more templates

const templates = new Map([
  ['clean-professional', cleanProfessionalTemplate],
  ['classic-blue', classicBlueTemplate],
  // ... more templates
]);

export function renderLetterTemplate(
  templateId: string,
  data: StructuredLetterData
): string {
  const template = templates.get(templateId);
  if (!template) {
    throw new Error(`Template ${templateId} not found`);
  }
  
  // Convert structured data to template data
  const templateData = convertToTemplateData(data);
  
  // Render template
  return template(templateData);
}
```

## 6. Migration Steps

### Phase 1: Foundation (Week 1-2)
1. Create structured data types and interfaces
2. Set up Handlebars compilation pipeline
3. Convert one template as proof of concept
4. Create template engine module

### Phase 2: Backend Updates (Week 3-4)
1. Update database schema
2. Create new API endpoints
3. Modify edge function for structured data
4. Update AI prompts for JSON output

### Phase 3: Frontend Integration (Week 5-6)
1. Create letter editing components
2. Implement live preview
3. Update generation hooks
4. Integrate with new API

### Phase 4: Template Migration (Week 7-8)
1. Convert all existing templates
2. Test each template thoroughly
3. Create template preview system
4. Update template selection UI

### Phase 5: Testing & Rollout (Week 9-10)
1. End-to-end testing
2. Performance testing
3. A/B testing setup
4. Gradual rollout

## 7. Risk Assessment & Mitigation

### 7.1 Technical Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| AI fails to generate valid JSON | High | Medium | Implement robust JSON parsing with fallbacks |
| Template rendering errors | High | Low | Comprehensive template testing suite |
| Performance degradation | Medium | Low | Implement caching and optimization |
| Data migration issues | High | Medium | Create rollback procedures |

### 7.2 Business Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| User experience disruption | High | Medium | A/B testing and gradual rollout |
| Template quality issues | Medium | Low | Extensive QA testing |
| Cost overruns | Low | Low | Detailed estimation and monitoring |

## 8. Success Metrics

### 8.1 Performance Metrics
- **Generation Time**: Reduce by 50% (from ~8s to ~4s)
- **API Costs**: Reduce by 60% (eliminate OpenAI calls)
- **Error Rate**: Maintain below 1%
- **Template Rendering**: < 100ms

### 8.2 User Experience Metrics
- **User Satisfaction**: Maintain or improve current levels
- **Template Usage**: Track template selection patterns
- **Edit Frequency**: Monitor how often users edit generated letters

### 8.3 Technical Metrics
- **Code Coverage**: > 80% for new modules
- **Template Test Coverage**: 100%
- **API Response Time**: < 200ms for template rendering

## 9. Rollback Strategy

### 9.1 Feature Flags
```typescript
const FEATURE_FLAGS = {
  USE_STRUCTURED_LETTER_GENERATION: false,
  ENABLE_LETTER_EDITING: false,
  USE_HANDLEBARS_TEMPLATES: false
};
```

### 9.2 Dual System Operation
- Maintain both generation systems during transition
- Route traffic based on feature flags
- Monitor metrics for both systems

### 9.3 Data Compatibility
- Ensure new system can read old letter format
- Create migration scripts for existing letters
- Maintain backward compatibility

## 10. Timeline & Resources

### 10.1 Timeline
- **Total Duration**: 10-12 weeks
- **Development**: 8 weeks
- **Testing**: 2 weeks
- **Rollout**: 2 weeks

### 10.2 Resource Requirements
- **Backend Developer**: 1 FTE for 10 weeks
- **Frontend Developer**: 1 FTE for 8 weeks
- **QA Engineer**: 0.5 FTE for 4 weeks
- **DevOps**: 0.25 FTE for 4 weeks

### 10.3 Dependencies
- Handlebars template compilation setup
- Database migration tools
- A/B testing infrastructure
- Monitoring and alerting setup

## Appendices

### A. Sample Structured Data Output
```json
{
  "metadata": {
    "generatedAt": "2024-01-29T10:00:00Z",
    "templateId": "clean-professional",
    "language": "id"
  },
  "header": {
    "date": "29 Januari 2024"
  },
  "subject": {
    "prefix": "Perihal: Lamaran Pekerjaan sebagai",
    "position": "Fullstack Developer"
  },
  "recipient": {
    "salutation": "Yth.",
    "title": "Bapak/Ibu Bagian Sumber Daya Manusia",
    "company": "PT Teknologi Indonesia"
  },
  "body": {
    "opening": "Dengan hormat,",
    "paragraphs": [
      "Berdasarkan informasi lowongan pekerjaan...",
      "Saya adalah lulusan Teknik Informatika...",
      "Pengalaman saya meliputi..."
    ],
    "closing": "Atas perhatian Bapak/Ibu, saya ucapkan terima kasih."
  },
  "signature": {
    "farewell": "Hormat saya,",
    "name": "John Doe"
  }
}
```

### B. Template Conversion Checklist
- [ ] Extract HTML structure
- [ ] Identify dynamic content
- [ ] Create Handlebars placeholders
- [ ] Extract and organize CSS
- [ ] Create template metadata
- [ ] Test with sample data
- [ ] Validate output format
- [ ] Performance testing

### C. API Documentation Updates
- New endpoints documentation
- Request/response formats
- Error codes and handling
- Rate limiting considerations
- Authentication requirements