# Letter Generation System - Async Migration Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture Changes](#architecture-changes)
3. [New Components and Responsibilities](#new-components-and-responsibilities)
4. [Database Schema Changes](#database-schema-changes)
5. [Realtime Updates](#realtime-updates)
6. [Migration Checklist](#migration-checklist)
7. [API Changes](#api-changes)
8. [Testing Guidelines](#testing-guidelines)

## Overview

This document details the migration of the letter generation system from **synchronous** to **asynchronous** processing. The new system provides better user experience, improved scalability, and real-time status updates during letter generation.

### Key Improvements
- **Non-blocking UI**: Users can interact with the application while letters are being generated
- **Real-time status updates**: Live progress tracking via Supabase realtime subscriptions
- **Better error handling**: Granular error tracking and recovery mechanisms
- **Scalability**: Edge function processing enables better resource utilization
- **Structured data support**: Enhanced letter generation with structured JSON data

### Migration Timeline
- **Before**: Synchronous API route processing (~30-60 seconds blocking)
- **After**: Async processing with immediate response (~100ms initial response, background processing)

## Architecture Changes

### Before (Synchronous)
```
Client Request → API Route → AI Generation → Template Rendering → Response
     ↓              ↓             ↓              ↓              ↓
  User waits    Blocking      Blocking       Blocking      Complete
```

### After (Asynchronous)
```
Client Request → API Route → Database Insert → Edge Function (Async)
     ↓              ↓             ↓                    ↓
  Immediate      Immediate    Record Created    Background Processing
  Response       Return           ↓                    ↓
                  ↓         Realtime Updates ← Status Updates
                Client                              ↓
             Subscribes to                   Template Rendering
              Status Updates                        ↓
                                            Database Update
```

### New Flow Diagram
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant DB as Database
    participant Edge as Edge Function
    participant AI as Google AI
    participant RT as Realtime

    Client->>API: POST /api/generate-application-letter
    API->>DB: INSERT letter with status='processing'
    API->>Client: Return letter ID immediately
    API->>Edge: Invoke generate-letter (async)
    
    Client->>RT: Subscribe to letter updates
    
    Edge->>DB: Update status='processing'
    Edge->>AI: Generate structured data
    AI-->>Edge: Return structured data
    Edge->>DB: Update with structured_data, status='done'
    
    DB->>RT: Notify status change
    RT->>Client: Real-time status update
```

## New Components and Responsibilities

### 1. Edge Function (`/web/supabase/functions/generate-letter/`)

**File**: [`index.ts`](../supabase/functions/generate-letter/index.ts)

**Responsibilities**:
- Process letter generation requests asynchronously
- Generate structured letter data using AI
- Update database with progress and results
- Handle error states and recovery

**Key Features**:
- Supports multiple resume formats (PDF, DOCX, PNG, JPG, TXT)
- Processes job descriptions and images
- Generates structured JSON data for templates
- Comprehensive error handling and logging

### 2. Letter Edge Function Utils (`/web/utils/letterEdgeFunction.ts`)

**File**: [`letterEdgeFunction.ts`](../utils/letterEdgeFunction.ts)

**Responsibilities**:
- Interface between client-side code and edge function
- Handle data serialization (ArrayBuffer ↔ Base64)
- Manage async letter generation lifecycle
- Provide polling and status checking utilities

**Key Functions**:
- `callGenerateLetterEdgeFunction()`: Invoke edge function
- `createLetterGenerationRequest()`: Create database record
- `getLetterGenerationStatus()`: Check generation status
- `pollLetterGenerationStatus()`: Poll for completion
- `initiateAsyncLetterGeneration()`: Start async generation

### 3. React Hook (`/web/hooks/useLetterGeneration.ts`)

**File**: [`useLetterGeneration.ts`](../hooks/useLetterGeneration.ts)

**Responsibilities**:
- Manage client-side generation state
- Subscribe to real-time updates via Supabase
- Provide status tracking and error handling
- Transform server data for UI consumption

**Hook States**:
```typescript
type LetterGenerationStatus =
  | { status: "idle" }
  | { status: "processing"; startedAt?: number }
  | { status: "done"; plainText: string; designHtml?: string; structuredData?: any }
  | { status: "error"; error: string; startedAt?: number }
```

### 4. Updated API Route (`/web/app/api/generate-application-letter/route.ts`)

**File**: [`route.ts`](../app/api/generate-application-letter/route.ts)

**Responsibilities**:
- Handle initial request processing
- Create database records
- Invoke edge function asynchronously
- Return immediate response to client

**Changes**:
- No longer blocks on AI generation
- Returns letter ID immediately
- Triggers background processing
- Improved error handling and validation

### 5. Frontend Integration (`/web/app/application-letter/page.tsx`)

**File**: [`page.tsx`](../app/application-letter/page.tsx)

**Responsibilities**:
- Use async generation hooks
- Display real-time progress updates
- Handle loading and error states
- Provide editing capabilities for generated letters

**UI Improvements**:
- Loading indicators during generation
- Real-time progress updates
- Non-blocking user interactions
- Enhanced error messaging

## Database Schema Changes

### Migration File
**File**: [`20250801082201_add_status_and_generation_columns_to_letters.sql`](../supabase/migrations/20250801082201_add_status_and_generation_columns_to_letters.sql)

### New Columns in `letters` Table

#### 1. Status Tracking
```sql
-- Status enum for tracking generation progress
CREATE TYPE "public"."letter_status" AS ENUM (
    'processing',
    'done', 
    'error'
);

ALTER TABLE "public"."letters" 
ADD COLUMN "status" "public"."letter_status" DEFAULT 'processing';
```

#### 2. Error Handling
```sql
-- Store detailed error messages
ALTER TABLE "public"."letters" 
ADD COLUMN "error_message" "text";
```

#### 3. Structured Data Storage
```sql
-- Store AI-generated structured letter data
ALTER TABLE "public"."letters" 
ADD COLUMN "structured_data" "jsonb";

-- Store metadata (job info, resume info, generation parameters)
ALTER TABLE "public"."letters" 
ADD COLUMN "data" "jsonb";
```

#### 4. Performance Indexes
```sql
-- Optimize common queries
CREATE INDEX "idx_letters_status" ON "public"."letters" USING "btree" ("status");
CREATE INDEX "idx_letters_structured_data" ON "public"."letters" USING "gin" ("structured_data");
CREATE INDEX "idx_letters_data" ON "public"."letters" USING "gin" ("data");
CREATE INDEX "idx_letters_created_at" ON "public"."letters" USING "btree" ("created_at" DESC);
CREATE INDEX "idx_letters_user_id_status" ON "public"."letters" USING "btree" ("user_id", "status");
```

### Schema Documentation Comments
```sql
COMMENT ON COLUMN "public"."letters"."status" IS 'Status of letter generation: processing, done, or error';
COMMENT ON COLUMN "public"."letters"."error_message" IS 'Error message when status is error';
COMMENT ON COLUMN "public"."letters"."structured_data" IS 'Structured data extracted from the letter content';
COMMENT ON COLUMN "public"."letters"."data" IS 'Metadata including job info, resume info, and other generation parameters';
```

### Data Structure Examples

#### Structured Data Format
```json
{
  "metadata": {
    "generatedAt": "2025-08-01T08:00:00.000Z",
    "lastModified": "2025-08-01T08:00:00.000Z",
    "templateId": "plain-text",
    "language": "id"
  },
  "header": {
    "date": "1 Agustus 2025"
  },
  "subject": {
    "prefix": "Perihal: Lamaran Pekerjaan sebagai",
    "position": "Software Engineer"
  },
  "recipient": {
    "salutation": "Yth.",
    "title": "Bapak/Ibu Bagian Sumber Daya Manusia",
    "company": "PT. Tech Company",
    "address": ["Jl. Technology Street No. 123", "Jakarta 12345"]
  },
  "body": {
    "opening": "Dengan hormat,",
    "paragraphs": [
      "Paragraph 1 content...",
      "Paragraph 2 content...",
      "Paragraph 3 content..."
    ],
    "closing": "Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih."
  },
  "signature": {
    "farewell": "Hormat saya,",
    "name": "Candidate Name"
  }
}
```

#### Metadata Format
```json
{
  "generatedAt": "2025-08-01T08:00:00.000Z",
  "templateId": "professional-classic",
  "resumeInput": {
    "file": {
      "mimeType": "application/pdf"
    }
  },
  "jobInput": {
    "description": "Software Engineer position...",
    "image": {
      "mimeType": "image/png"
    }
  }
}
```

## Realtime Updates

### Supabase Realtime Integration

The system uses Supabase's realtime capabilities to provide live updates during letter generation.

#### Client-Side Subscription
```typescript
// From useLetterGeneration.ts
const subscription = supabase
  .channel(`letter_${id}_changes`)
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'letters',
      filter: `id=eq.${id}`
    },
    (payload) => {
      console.log('Realtime letter status change:', payload);
      const updatedStatus = transformLetterData(payload.new);
      setStatus(updatedStatus);
    }
  )
  .subscribe();
```

#### Status Transformation
```typescript
const transformLetterData = (letter: any): LetterGenerationStatus => {
  switch (letter.status) {
    case 'done':
      return {
        status: "done",
        plainText: letter.plain_text || '',
        designHtml: letter.design_html || undefined,
        startedAt: new Date(letter.created_at).getTime(),
        structuredData: letter.structured_data
      };
    case 'error':
      return {
        status: "error",
        error: letter.error_message || 'Generation failed',
        startedAt: new Date(letter.created_at).getTime()
      };
    case 'processing':
    default:
      return {
        status: "processing",
        startedAt: new Date(letter.created_at).getTime()
      };
  }
};
```

### Update Flow
1. **Edge Function Updates Database** → Triggers realtime event
2. **Supabase Realtime** → Broadcasts change to subscribed clients
3. **Client Hook** → Receives update and transforms data
4. **React Component** → Re-renders with new status

### Benefits
- **Immediate Feedback**: Users see status changes instantly
- **Multiple Clients**: All connected clients receive updates
- **Automatic Cleanup**: Subscriptions are cleaned up on unmount
- **Error Propagation**: Errors are communicated in real-time

## Migration Checklist

### Pre-Migration
- [ ] **Database Backup**: Create full backup before running migration
- [ ] **Environment Variables**: Ensure all required variables are set
  - `SUPABASE_URL`
  - `SUPABASE_SERVICE_ROLE_KEY` 
  - `GOOGLE_AI_API_KEY`
- [ ] **Edge Function Deployment**: Deploy the `generate-letter` function
- [ ] **Testing Environment**: Verify async flow in staging

### Migration Steps
- [ ] **Run Database Migration**
  ```bash
  supabase db push
  ```
- [ ] **Deploy Edge Function**
  ```bash
  supabase functions deploy generate-letter
  ```
- [ ] **Update Frontend Code**: Deploy new React components and hooks
- [ ] **Monitor Initial Requests**: Watch for errors in early usage
- [ ] **Performance Testing**: Verify improved response times

### Post-Migration Verification
- [ ] **Status Updates**: Confirm realtime updates work correctly
- [ ] **Error Handling**: Test error scenarios and recovery
- [ ] **Template Rendering**: Verify structured data → HTML conversion
- [ ] **Concurrent Requests**: Test multiple simultaneous generations
- [ ] **Historical Data**: Ensure existing letters still accessible

### Rollback Plan
If issues arise, the system can be rolled back by:
1. **Disable Async Routes**: Route traffic to backup sync endpoints
2. **Database Rollback**: Revert migration if necessary
3. **Edge Function Disable**: Remove edge function from routing
4. **Frontend Rollback**: Deploy previous synchronous version

### Performance Expectations
- **Initial Response Time**: < 200ms (down from 30-60 seconds)
- **Background Processing**: 15-45 seconds depending on complexity
- **Concurrent Capacity**: 10x improvement in simultaneous requests
- **Memory Usage**: Reduced frontend memory footprint
- **User Experience**: Non-blocking, responsive interface

## API Changes

### Request/Response Format Changes

#### Before (Synchronous)
```typescript
// Request
POST /api/generate-application-letter
Content-Type: multipart/form-data

// Response (after 30-60 seconds)
{
  "success": true,
  "data": {
    "plainText": "Generated letter content...",
    "design": {
      "html": "<html>...</html>",
      "templateId": "classic-blue"
    },
    "letterId": "uuid"
  }
}
```

#### After (Asynchronous)
```typescript
// Request (same)
POST /api/generate-application-letter
Content-Type: multipart/form-data

// Response (immediate, ~100ms)
{
  "id": "letter-uuid-here"
}

// Status checking via realtime subscription or polling
// Final state received via realtime updates
```

### New Edge Function API

#### Request Format
```typescript
POST /functions/v1/generate-letter
Content-Type: application/json

{
  "letterId": "uuid-of-existing-letter",
  "resumeInput": {
    "file": {
      "buffer": "base64-encoded-content",
      "mimeType": "application/pdf",
      "extractedText": "text-for-docx-files" // optional
    }
    // OR
    "manual": {
      "fullName": "Name",
      "professionalTitle": "Title",
      "professionalSummary": "Summary",
      "mostRecentJob": { ... },
      "skills": "Skills list"
    }
  },
  "jobInput": {
    "description": "Job description text",
    "image": {
      "buffer": "base64-encoded-image",
      "mimeType": "image/png"
    }
  },
  "templateId": "template-identifier"
}
```

#### Response Format
```typescript
// Success
{
  "success": true,
  "letterId": "uuid",
  "message": "Letter generated successfully",
  "structuredData": { ... }
}

// Error
{
  "success": false,
  "error": "Error description",
  "errorType": "ErrorType",
  "timestamp": "2025-08-01T08:00:00.000Z"
}
```

### New Hook Interface

#### `startGeneration()` Function
```typescript
interface LetterGenerationParams {
  jobDescription?: string;
  jobImage?: File;
  unauthenticatedResumeFile?: File;
  unauthenticatedResumeFileName?: string;
  templateId?: string;
}

async function startGeneration(params: LetterGenerationParams): Promise<string>
```

#### `useGenerationStatus()` Hook
```typescript
function useGenerationStatus(id: string | null): LetterGenerationStatus

type LetterGenerationStatus =
  | { status: "idle" }
  | { status: "processing"; startedAt?: number }
  | { status: "done"; plainText: string; designHtml?: string; structuredData?: any }
  | { status: "error"; error: string; startedAt?: number }
```

### Error Handling Changes

#### Before
- Errors thrown immediately during request
- Limited error context
- Request timeout issues

#### After
- Errors stored in database with detailed context
- Real-time error propagation
- Graceful failure recovery
- Comprehensive error logging

#### Error Types
```typescript
interface ErrorStates {
  // Client-side errors
  "validation_error": "Invalid input parameters",
  "authentication_error": "User not authenticated",
  "insufficient_tokens": "Not enough tokens for template",
  
  // Server-side errors  
  "ai_generation_error": "AI service failure",
  "template_rendering_error": "Template processing failure",
  "database_error": "Database operation failure",
  
  // Edge function errors
  "environment_error": "Missing environment variables",
  "file_processing_error": "File format/processing issues",
  "timeout_error": "Generation timeout"
}
```

### Backward Compatibility

The system maintains backward compatibility for:
- **Existing API endpoints**: Old sync endpoints remain functional
- **Database structure**: New columns are optional/nullable
- **Frontend components**: Graceful fallback to sync mode if needed

### Migration Support
- **Feature flags**: Toggle between sync/async modes
- **Gradual rollout**: Enable async for percentage of users
- **A/B testing**: Compare performance between versions

## Testing Guidelines

### Unit Testing

#### Edge Function Testing
```typescript
// Test AI generation
describe('generateStructuredLetterData', () => {
  it('should generate valid structured data', async () => {
    const resumeInput = { file: { buffer: mockPdf, mimeType: 'application/pdf' } };
    const jobInput = { description: 'Software Engineer role' };
    
    const result = await generateStructuredLetterData(
      resumeInput, jobInput, 'mock-api-key', 'plain-text', 'id'
    );
    
    expect(result).toHaveProperty('metadata');
    expect(result).toHaveProperty('body.paragraphs');
    expect(result.body.paragraphs).toHaveLength(3);
  });
  
  it('should handle errors gracefully', async () => {
    // Mock AI service error
    mockAIService.mockRejectedValue(new Error('AI service down'));
    
    await expect(generateStructuredLetterData(...args))
      .rejects.toThrow('AI service down');
  });
});
```

#### Hook Testing
```typescript
// Test useGenerationStatus hook
describe('useGenerationStatus', () => {
  it('should handle status transitions', async () => {
    const { result } = renderHook(() => useGenerationStatus('test-id'));
    
    expect(result.current.status).toBe('processing');
    
    // Simulate realtime update
    act(() => {
      mockSupabaseUpdate({ status: 'done', structured_data: mockData });
    });
    
    expect(result.current.status).toBe('done');
    expect(result.current.structuredData).toBeDefined();
  });
});
```

### Integration Testing

#### End-to-End Flow Testing
```typescript
describe('Letter Generation Integration', () => {
  it('should complete full async generation flow', async () => {
    // 1. Start generation
    const formData = new FormData();
    formData.append('jobDescription', 'Test job');
    formData.append('templateId', 'plain-text');
    
    const response = await POST(createRequest(formData));
    const { id } = await response.json();
    
    // 2. Verify database record created
    const letter = await getLetterById(id);
    expect(letter.status).toBe('processing');
    
    // 3. Wait for completion (in real test, mock this)
    await waitFor(() => {
      expect(letter.status).toBe('done');
    });
    
    // 4. Verify structured data
    expect(letter.structured_data).toBeDefined();
    expect(letter.structured_data.body.paragraphs).toHaveLength(3);
  });
});
```

### Performance Testing

#### Load Testing
```typescript
describe('Concurrent Generation Load', () => {
  it('should handle multiple simultaneous requests', async () => {
    const requests = Array.from({ length: 10 }, () => 
      createGenerationRequest()
    );
    
    const startTime = performance.now();
    const responses = await Promise.all(requests);
    const endTime = performance.now();
    
    // All should return immediately with IDs
    responses.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.data.id).toBeDefined();
    });
    
    // Should complete within reasonable time
    expect(endTime - startTime).toBeLessThan(5000);
  });
});
```

#### Memory Usage Testing
```typescript
describe('Memory Usage', () => {
  it('should not leak memory during generation', async () => {
    const initialMemory = process.memoryUsage().heapUsed;
    
    // Generate multiple letters
    for (let i = 0; i < 50; i++) {
      await generateLetter(mockParams);
    }
    
    // Force garbage collection
    if (global.gc) global.gc();
    
    const finalMemory = process.memoryUsage().heapUsed;
    const memoryGrowth = finalMemory - initialMemory;
    
    // Memory growth should be within acceptable limits
    expect(memoryGrowth).toBeLessThan(100 * 1024 * 1024); // 100MB
  });
});
```

### Database Testing

#### Migration Testing
```sql
-- Test migration rollback capability
BEGIN;
  -- Apply migration
  \i migrations/20250801082201_add_status_and_generation_columns_to_letters.sql
  
  -- Test data insertion
  INSERT INTO letters (user_id, status, structured_data) 
  VALUES ('test-user', 'processing', '{"test": true}');
  
  -- Test indexes work
  EXPLAIN (ANALYZE, BUFFERS) 
  SELECT * FROM letters WHERE status = 'processing';
  
ROLLBACK; -- Don't commit in test
```

#### Realtime Testing
```typescript
describe('Realtime Updates', () => {
  it('should receive status updates via subscription', async () => {
    let receivedUpdate = false;
    
    // Setup subscription
    const subscription = supabase
      .channel('letter_test_changes')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'letters',
        filter: 'id=eq.test-id'
      }, (payload) => {
        receivedUpdate = true;
        expect(payload.new.status).toBe('done');
      })
      .subscribe();
    
    // Trigger update
    await supabase
      .from('letters')
      .update({ status: 'done' })
      .eq('id', 'test-id');
    
    // Wait for realtime event
    await waitFor(() => {
      expect(receivedUpdate).toBe(true);
    });
    
    supabase.removeChannel(subscription);
  });
});
```

### Error Scenario Testing

#### Edge Function Error Handling
```typescript
describe('Edge Function Error Handling', () => {
  it('should handle AI service failures', async () => {
    // Mock AI service failure
    mockGoogleAI.mockRejectedValue(new Error('Service unavailable'));
    
    const response = await invokeEdgeFunction({
      letterId: 'test-id',
      resumeInput: mockResumeInput,
      jobInput: mockJobInput
    });
    
    expect(response.success).toBe(false);
    expect(response.error).toContain('Service unavailable');
    
    // Verify database updated with error
    const letter = await getLetterById('test-id');
    expect(letter.status).toBe('error');
    expect(letter.error_message).toBeDefined();
  });
  
  it('should handle malformed input data', async () => {
    const invalidRequest = {
      letterId: 'test-id',
      // Missing required fields
    };
    
    const response = await invokeEdgeFunction(invalidRequest);
    
    expect(response.success).toBe(false);
    expect(response.error).toContain('Missing required parameters');
  });
});
```

### Testing Tools and Setup

#### Required Dependencies
```json
{
  "devDependencies": {
    "@testing-library/react": "^14.0.0",
    "@testing-library/react-hooks": "^8.0.1",
    "@testing-library/jest-dom": "^6.0.0",
    "jest": "^29.0.0",
    "supertest": "^6.3.0",
    "msw": "^2.0.0"
  }
}
```

#### Test Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1'
  },
  testMatch: [
    '**/__tests__/**/*.test.{js,ts,tsx}',
    '**/?(*.)+(spec|test).{js,ts,tsx}'
  ]
};
```

#### Mock Setup
```typescript
// __tests__/utils/test-helpers.ts
export const mockSupabaseClient = {
  from: jest.fn(() => ({
    insert: jest.fn(() => ({ select: jest.fn(() => ({ single: jest.fn() })) })),
    update: jest.fn(() => ({ eq: jest.fn() })),
    select: jest.fn(() => ({ eq: jest.fn(() => ({ single: jest.fn() })) }))
  })),
  functions: {
    invoke: jest.fn()
  },
  channel: jest.fn(() => ({
    on: jest.fn(() => ({ subscribe: jest.fn() })),
    subscribe: jest.fn()
  }))
};
```

### Testing Best Practices

1. **Test Isolation**: Each test should be independent and cleanup after itself
2. **Mock External Services**: Don't hit real APIs during testing
3. **Async Testing**: Properly handle promises and async operations
4. **Error Scenarios**: Test failure cases as thoroughly as success cases
5. **Performance Benchmarks**: Include performance assertions in critical paths
6. **Database Cleanup**: Reset database state between tests
7. **Realtime Testing**: Test subscription setup, updates, and cleanup

---

## Additional Resources

### File References
- [Database Migration](../supabase/migrations/20250801082201_add_status_and_generation_columns_to_letters.sql)
- [Edge Function](../supabase/functions/generate-letter/index.ts)
- [Edge Function Utils](../utils/letterEdgeFunction.ts)
- [React Hook](../hooks/useLetterGeneration.ts)
- [API Route](../app/api/generate-application-letter/route.ts)
- [Frontend Component](../app/application-letter/page.tsx)
- [Integration Tests](../__tests__/letter-generation-integration.test.ts)

### Related Documentation
- [Supabase Edge Functions Guide](https://supabase.com/docs/guides/functions)
- [Supabase Realtime Documentation](https://supabase.com/docs/guides/realtime)
- [Google AI Generative API](https://ai.google.dev/docs)

### Support and Troubleshooting
For issues with the async letter generation system:
1. Check Edge Function logs in Supabase dashboard
2. Monitor database status column values
3. Verify realtime subscription connections
4. Review API route error logs
5. Test with simplified input data

This migration significantly improves the user experience and system scalability while maintaining backward compatibility and providing comprehensive error handling.