# Letter Generation Migration Guide

This document outlines the migration from the old AI-based HTML generation approach to the new structured data approach for application letter generation.

## Overview

The application letter generation system has been refactored to use a more structured and maintainable approach:

- **Old Approach**: Plain text generation → OpenAI HTML template generation
- **New Approach**: Structured data generation → Handlebars template rendering

## What Was Removed

### 1. OpenAI Dependencies
- Removed OpenAI client initialization from `applicationLetterGenerator.ts`
- Removed OpenAI-based HTML template generation
- Removed OpenAI API key dependency (`NEXT_PUBLIC_OPENAI_API_KEY`)

### 2. Legacy Generation Paths
- Marked legacy streaming functions as deprecated
- Added deprecation warnings to old generation methods
- Updated API routes to indicate deprecated paths

### 3. Deprecated Files
The following files are now deprecated but kept for backward compatibility:

- `web/utils/ai-generators/applicationLetterGenerator.ts` - Legacy HTML generation
- `web/utils/ai-generators/applicationLetterGeneratorStreaming.ts` - Legacy streaming approach

## New Structured Approach

### Key Components

1. **Structured Data Generator** (`structuredLetterGenerator.ts`)
   - Generates structured JSON data instead of HTML
   - Uses Gemini API for consistent data structure
   - Provides better error handling and validation

2. **Template Engine** (`letter-template-engine.ts`)
   - Uses Handlebars templates for consistent rendering
   - Separates content from presentation
   - Easier to maintain and modify templates

3. **Unified Edge Function** (`generate-letter-unified.ts`)
   - Single endpoint for structured generation
   - Supports streaming for better UX
   - Uses only Gemini API (no OpenAI dependency)

### Structured Data Format

The new approach generates structured data with the following format:

```json
{
  "metadata": {
    "generatedAt": "2025-01-01T00:00:00.000Z",
    "templateId": "professional-classic",
    "language": "id"
  },
  "header": {
    "date": "1 Januari 2025"
  },
  "subject": {
    "prefix": "Perihal: Lamaran Pekerjaan sebagai",
    "position": "Software Developer"
  },
  "recipient": {
    "salutation": "Yth.",
    "title": "Bapak/Ibu Bagian Sumber Daya Manusia",
    "company": "PT Example Company",
    "address": ["Jl. Example No. 123", "Jakarta 12345"]
  },
  "body": {
    "opening": "Dengan hormat,",
    "paragraphs": [
      "First paragraph content...",
      "Second paragraph content...",
      "Third paragraph content..."
    ],
    "closing": "Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih."
  },
  "signature": {
    "farewell": "Hormat saya,",
    "name": "John Doe",
    "additionalInfo": "Email: <EMAIL>"
  },
  "attachments": ["Curriculum Vitae", "Portofolio"]
}
```

## Migration Steps

### For New Features
1. Use `generateStructuredLetterData()` from `structuredLetterGenerator.ts`
2. Use `fillLetterTemplate()` from `letter-template-engine.ts` for rendering
3. Use the unified edge function `/api/edge/generate-letter-unified` for streaming

### For Existing Code
1. **Immediate**: No breaking changes - old code still works with deprecation warnings
2. **Short term**: Update clients to use `useStructuredGeneration: true` flag
3. **Long term**: Migrate to direct structured generation calls

### Template Updates
Templates now use Handlebars syntax instead of being filled by OpenAI:

**Old approach**: AI fills HTML template
**New approach**: Structured data + Handlebars template

Example template syntax:
```handlebars
<div class="date">{{header.date}}</div>
<div class="subject">{{subject.prefix}} {{subject.position}}</div>
<div class="recipient">
  {{recipient.salutation}} {{recipient.title}}
  {{#if recipient.company}}
  <br>{{recipient.company}}
  {{/if}}
</div>
```

## Breaking Changes

### Removed Dependencies
- `openai` package no longer required
- `NEXT_PUBLIC_OPENAI_API_KEY` environment variable no longer needed

### API Changes
- Legacy generation paths marked as deprecated
- New structured generation preferred
- Unified streaming endpoint recommended

### Template Changes
- Templates now use Handlebars instead of being AI-generated
- More predictable and maintainable output
- Better error handling and validation

## Benefits of New Approach

1. **Performance**: Faster generation without OpenAI dependency
2. **Reliability**: More predictable output with structured data
3. **Maintainability**: Easier template modifications
4. **Cost**: Reduced API costs (only Gemini, no OpenAI)
5. **Flexibility**: Better support for editing and customization
6. **Validation**: Built-in data validation and error handling

## Timeline

- **Phase 1** (Current): Deprecation warnings added, both approaches work
- **Phase 2** (Next): Encourage migration to structured approach
- **Phase 3** (Future): Remove legacy code entirely

## Support

For questions about migration:
1. Check the new structured generator implementation
2. Review template engine documentation
3. Test with the unified edge function
4. Update feature flags to use structured generation

## Example Migration

### Old Code
```typescript
const result = await generateAIApplicationLetter(
  resumeFile,
  userId,
  jobDescription,
  jobImage,
  templateId
);
```

### New Code
```typescript
// Generate structured data
const structuredData = await generateStructuredLetterData(
  resumeFile,
  jobDescription,
  jobImage,
  { templateId, language: 'id' }
);

// Render with template
const template = getTemplateById(templateId);
const html = fillLetterTemplate(template, structuredData);
```

This migration ensures better performance, maintainability, and user experience while maintaining backward compatibility during the transition period.