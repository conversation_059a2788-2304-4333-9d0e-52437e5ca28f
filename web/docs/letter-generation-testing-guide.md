# Letter Generation System - Testing Guide

## Table of Contents
1. [Overview](#overview)
2. [Setup Requirements](#setup-requirements)
3. [Pre-Testing Checklist](#pre-testing-checklist)
4. [Manual Testing Procedures](#manual-testing-procedures)
5. [Database Verification](#database-verification)
6. [Realtime Updates Testing](#realtime-updates-testing)
7. [Edge Cases and Error Scenario<PERSON>](#edge-cases-and-error-scenarios)
8. [Common Issues and Troubleshooting](#common-issues-and-troubleshooting)
9. [Performance Testing](#performance-testing)
10. [Testing Checklist](#testing-checklist)

## Overview

This guide provides comprehensive testing procedures for the asynchronous letter generation system. The system processes letter generation requests through a multi-stage async pipeline involving API routes, edge functions, database updates, and realtime notifications.

### System Architecture Overview

```
Client Request → API Route → Database Insert → Edge Function (Async)
     ↓              ↓             ↓                    ↓
  Immediate      Immediate    Record Created    Background Processing
  Response       Return           ↓                    ↓
                   ↓         Realtime Updates ← Status Updates
                 Client                              ↓
              Subscribes to                   Template Rendering
               Status Updates                        ↓
                                             Database Update
```

## Setup Requirements

### Environment Variables

Ensure the following environment variables are configured:

**Backend (.env.local)**:
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Google AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_api_key

# Additional Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

**Supabase Edge Function**:
```bash
# Set via Supabase CLI or Dashboard
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
GOOGLE_AI_API_KEY=your_google_ai_api_key
```

### Database Schema

Verify the following database migration has been applied:

```sql
-- Check if letter_status enum exists
SELECT * FROM pg_type WHERE typname = 'letter_status';

-- Check if new columns exist in letters table
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'letters' 
AND column_name IN ('status', 'error_message', 'structured_data', 'data');

-- Check if indexes exist
SELECT indexname FROM pg_indexes WHERE tablename = 'letters';
```

### Edge Function Deployment

Verify the edge function is deployed:

```bash
# Check edge function status
supabase functions list

# Deploy if needed
supabase functions deploy generate-letter
```

### Test Data Requirements

Prepare the following test files:

1. **Resume Files**:
   - Valid PDF resume (< 5MB)
   - Valid DOCX resume (< 5MB)
   - Valid PNG/JPG resume image (< 5MB)
   - Invalid file formats (for error testing)
   - Corrupted files (for error testing)

2. **Job Posting Files**:
   - Clear job posting images (PNG/JPG)
   - Job descriptions with company names and position titles
   - Incomplete job information (for edge case testing)

## Pre-Testing Checklist

Before starting tests, verify:

- [ ] All environment variables are set correctly
- [ ] Database migration is applied
- [ ] Edge function is deployed and accessible
- [ ] Realtime subscriptions are enabled in Supabase
- [ ] Google AI API key has sufficient quota
- [ ] Test user accounts are created
- [ ] Test files are prepared
- [ ] Development server is running

## Manual Testing Procedures

### Test 1: Complete Happy Path Flow

**Objective**: Verify the entire letter generation process works correctly.

**Steps**:

1. **Navigate to Application Letter Page**
   ```
   URL: http://localhost:3000/application-letter
   ```
   - **Verify**: Page loads without errors
   - **Verify**: Step indicator shows "Step 1: Upload Resume"

2. **Upload Resume (Step 1)**
   - Click file upload area
   - Select a valid PDF resume file
   - **Verify**: File uploads successfully
   - **Verify**: Green success message appears
   - **Verify**: "Selanjutnya" button is enabled
   - Click "Selanjutnya"
   - **Verify**: Navigation moves to Step 2

3. **Add Job Information (Step 2)**
   - Enter job description in text area:
     ```
     Software Engineer position at PT Example Company
     Requirements: JavaScript, React, Node.js
     Located in Jakarta, Indonesia
     ```
   - **Verify**: Text is entered correctly
   - **Verify**: "Selanjutnya" button is enabled
   - Click "Selanjutnya"
   - **Verify**: Navigation moves to Step 3

4. **Select Template (Step 3)**
   - **Verify**: Template grid displays available templates
   - **Verify**: Template previews load correctly
   - Click on "Plain Text" template
   - **Verify**: Template is selected (blue border appears)
   - **Verify**: "Buat Surat" button shows token cost
   - Click "Buat Surat"
   - **Verify**: Navigation moves to Step 4
   - **Verify**: Loading state appears

5. **View Results (Step 4)**
   - **Verify**: Loading spinner appears with progress text
   - Wait for generation to complete (15-45 seconds)
   - **Verify**: Loading disappears when generation completes
   - **Verify**: Generated letter text appears
   - **Verify**: Template design preview loads
   - **Verify**: "Unduh PDF" button is available
   - **Verify**: Letter content includes:
     - Proper Indonesian formatting
     - Company name extracted from job description
     - Position title extracted from job description
     - Candidate name from resume
     - Three substantial paragraphs

6. **Download PDF**
   - Click "Unduh PDF" button
   - **Verify**: Download starts immediately
   - **Verify**: PDF file downloads successfully
   - **Verify**: PDF contains formatted letter with template design

**Expected Results**:
- Complete flow takes 20-60 seconds total
- No errors or crashes occur
- Generated letter is relevant and well-formatted
- PDF download works correctly

### Test 2: Realtime Status Updates

**Objective**: Verify realtime status updates work during generation.

**Steps**:

1. **Setup Multiple Browser Windows**
   - Open the same user session in two browser windows
   - Navigate both to the application letter page

2. **Monitor Database Changes**
   - Open Supabase dashboard
   - Navigate to Table Editor → letters table
   - Keep this tab open for monitoring

3. **Start Letter Generation**
   - In first browser window, complete steps 1-3
   - Click "Buat Surat" and immediately switch to second window

4. **Verify Realtime Updates**
   - **Verify**: Both windows show loading state simultaneously
   - **Verify**: Database shows new record with status 'processing'
   - **Verify**: Both windows update when generation completes
   - **Verify**: Database status changes to 'done' when complete
   - **Verify**: structured_data column populates with JSON

**Expected Results**:
- Both browser windows stay synchronized
- Database updates are reflected in real-time
- No delays or inconsistencies in status updates

### Test 3: Authentication Flow

**Objective**: Test letter generation with different user states.

**Test 3A: Authenticated User**

**Steps**:
1. Log in as a test user
2. Complete letter generation flow
3. **Verify**: User's token balance decreases appropriately
4. **Verify**: Letter is saved to user's history
5. **Verify**: "Lihat Riwayat Surat" button appears after generation

**Test 3B: Unauthenticated User**

**Steps**:
1. Log out or use incognito mode
2. Start letter generation process
3. **Verify**: Upload works with unauthenticated files
4. **Verify**: Generation works without user account
5. **Verify**: Letter is not saved to history
6. **Verify**: Warning about file persistence appears

### Test 4: File Format Support

**Objective**: Test all supported resume file formats.

**Test 4A: PDF Resume**
- Upload PDF resume file
- **Verify**: File uploads successfully
- **Verify**: Generation works correctly
- **Verify**: Candidate name extracted properly

**Test 4B: DOCX Resume**
- Upload DOCX resume file
- **Verify**: File uploads successfully
- **Verify**: Text extraction works
- **Verify**: Generation uses extracted text

**Test 4C: Image Resume (PNG/JPG)**
- Upload PNG or JPG resume image
- **Verify**: File uploads successfully
- **Verify**: AI processes image correctly
- **Verify**: Text content extracted from image

### Test 5: Job Input Methods

**Objective**: Test different job input methods.

**Test 5A: Text Input Only**
- Use only job description text
- **Verify**: Generation works with text only
- **Verify**: Company and position extracted correctly

**Test 5B: Image Input Only**
- Upload job posting image without text
- **Verify**: Generation works with image only
- **Verify**: Job details extracted from image

**Test 5C: Combined Input**
- Use both text description and job image
- **Verify**: Both inputs are processed
- **Verify**: Information is combined appropriately

## Database Verification

### Checking Letter Records

**Query to check recent letters**:
```sql
SELECT 
    id,
    user_id,
    status,
    error_message,
    created_at,
    updated_at,
    structured_data->'metadata'->>'templateId' as template_id,
    structured_data->'subject'->>'position' as position,
    data->'generatedAt' as generated_at
FROM letters 
ORDER BY created_at DESC 
LIMIT 10;
```

**What to verify**:
- [ ] Status progresses from 'processing' to 'done' or 'error'
- [ ] structured_data contains valid JSON with all required fields
- [ ] Timestamps are accurate and logical
- [ ] User associations are correct
- [ ] Template IDs match what was selected

### Checking Status Transitions

**Query to track status changes**:
```sql
-- Check for letters stuck in processing
SELECT id, user_id, created_at, updated_at, status
FROM letters 
WHERE status = 'processing' 
AND created_at < NOW() - INTERVAL '5 minutes';

-- Check error patterns
SELECT error_message, COUNT(*) as count
FROM letters 
WHERE status = 'error' 
GROUP BY error_message 
ORDER BY count DESC;
```

### Verifying Data Integrity

**Check structured data format**:
```sql
SELECT 
    id,
    structured_data ? 'metadata' as has_metadata,
    structured_data ? 'body' as has_body,
    structured_data ? 'signature' as has_signature,
    jsonb_array_length(structured_data->'body'->'paragraphs') as paragraph_count
FROM letters 
WHERE status = 'done' 
AND structured_data IS NOT NULL;
```

**What to verify**:
- [ ] All completed letters have structured_data
- [ ] Structured data contains required fields
- [ ] Paragraphs array contains exactly 3 elements
- [ ] No malformed JSON in database

## Realtime Updates Testing

### Testing Subscription Setup

**Monitor subscription logs**:
```javascript
// In browser console during generation
console.log('Subscription status:', supabase.channel('letter_123_changes').state);
```

**What to verify**:
- [ ] Subscription establishes successfully
- [ ] Connection remains stable during generation
- [ ] Updates are received within 1-2 seconds of database changes
- [ ] Subscription cleans up properly when component unmounts

### Testing Update Reception

**Steps**:
1. Start letter generation
2. Manually update database record:
   ```sql
   UPDATE letters 
   SET status = 'done', 
       structured_data = '{"test": "data"}', 
       updated_at = NOW() 
   WHERE id = 'your-letter-id';
   ```
3. **Verify**: Frontend immediately reflects the update
4. **Verify**: UI transitions from loading to results state

### Testing Connection Resilience

**Steps**:
1. Start letter generation
2. Temporarily disconnect internet
3. Reconnect after 10 seconds
4. **Verify**: Updates are received once connection is restored
5. **Verify**: No duplicate updates or state inconsistencies

## Edge Cases and Error Scenarios

### Test 6: Invalid File Formats

**Objective**: Verify proper error handling for unsupported files.

**Steps**:
1. Try to upload unsupported file formats:
   - .txt file as resume
   - .xlsx file as resume  
   - .gif image as job posting
   - Files larger than 5MB

**Expected Results**:
- Clear error messages for each invalid format
- Upload process stops before submission
- No server errors or crashes

### Test 7: Corrupted Files

**Objective**: Test handling of corrupted or empty files.

**Steps**:
1. Upload corrupted PDF file
2. Upload empty file (0 bytes)
3. Upload file with wrong extension (.pdf that's actually .txt)

**Expected Results**:
- Appropriate error messages
- System gracefully handles errors
- Database records error states properly

### Test 8: Network Interruptions

**Objective**: Test system behavior during network issues.

**Steps**:
1. Start letter generation
2. Disconnect network during edge function processing
3. Reconnect network after 30 seconds
4. **Verify**: System recovers appropriately
5. **Verify**: User receives appropriate feedback

### Test 9: Edge Function Failures

**Objective**: Test handling when edge function fails.

**Steps**:
1. Temporarily break edge function (invalid environment variable)
2. Start letter generation
3. **Verify**: Database record status updates to 'error'
4. **Verify**: Error message is stored in database
5. **Verify**: User sees error message in UI
6. **Verify**: Realtime updates notify of error state

### Test 10: AI Service Failures

**Objective**: Test handling of Google AI API failures.

**Steps**:
1. Use invalid Google AI API key
2. Exceed API quota/rate limits
3. Send malformed data to AI service

**Expected Results**:
- Appropriate error messages
- Database records error states
- User receives actionable feedback

### Test 11: Insufficient Tokens

**Objective**: Test token validation for premium templates.

**Steps**:
1. Set user token balance to 0
2. Try to generate letter with premium template
3. **Verify**: Error message about insufficient tokens
4. **Verify**: "Beli Token" button appears
5. **Verify**: Generation does not proceed

### Test 12: Concurrent Requests

**Objective**: Test system behavior with multiple simultaneous requests.

**Steps**:
1. Start 5 letter generations simultaneously from different browsers
2. **Verify**: All requests are handled correctly
3. **Verify**: No race conditions or data corruption
4. **Verify**: Each gets proper realtime updates
5. **Verify**: Database maintains data integrity

### Test 13: Large File Processing

**Objective**: Test handling of maximum file sizes.

**Steps**:
1. Upload 4.9MB PDF resume (near limit)
2. Upload 5.1MB PDF resume (over limit)
3. **Verify**: Near-limit file processes correctly
4. **Verify**: Over-limit file is rejected with clear message

### Test 14: Malicious Content

**Objective**: Test security measures for malicious input.

**Steps**:
1. Upload file with script injection attempts
2. Enter HTML/JavaScript in job description
3. Test SQL injection in form fields

**Expected Results**:
- All inputs are properly sanitized
- No script execution in UI
- No database corruption

## Common Issues and Troubleshooting

### Issue 1: Generation Stuck in Processing

**Symptoms**:
- Letter status remains 'processing' for more than 5 minutes
- No error messages in logs
- Realtime updates not received

**Diagnosis Steps**:
1. Check edge function logs:
   ```bash
   supabase functions logs generate-letter
   ```
2. Check database record:
   ```sql
   SELECT * FROM letters WHERE id = 'stuck-letter-id';
   ```
3. Check environment variables in edge function

**Common Causes**:
- Edge function not deployed or crashed
- Invalid Google AI API key
- Network connectivity issues
- Malformed input data

**Resolution**:
1. Redeploy edge function
2. Verify environment variables
3. Check API quotas
4. Manually update stuck records to 'error' state

### Issue 2: Realtime Updates Not Working

**Symptoms**:
- Loading state persists after generation completes
- Manual database checks show 'done' status
- No error messages in console

**Diagnosis Steps**:
1. Check subscription status in browser console
2. Verify Supabase realtime is enabled
3. Check network connectivity
4. Test with different browsers

**Common Causes**:
- Realtime disabled in Supabase project
- Firewall blocking WebSocket connections
- Subscription channel name mismatch
- Client-side JavaScript errors

**Resolution**:
1. Enable realtime in Supabase dashboard
2. Check firewall settings
3. Verify subscription channel naming
4. Clear browser cache and cookies

### Issue 3: PDF Generation Failures

**Symptoms**:
- Letter generates successfully
- "Unduh PDF" button causes errors
- Download fails or produces corrupt files

**Diagnosis Steps**:
1. Check PDF generation service logs
2. Verify template HTML is valid
3. Test with different templates
4. Check file permissions

**Common Causes**:
- Invalid HTML in template
- Missing CSS dependencies
- Service configuration issues
- File system permissions

### Issue 4: Template Selection Issues

**Symptoms**:
- Templates don't load or display incorrectly
- Selection doesn't persist
- Generation uses wrong template

**Diagnosis Steps**:
1. Check template configuration files
2. Verify image assets are accessible
3. Check template ID mapping
4. Test with different templates

### Issue 5: File Upload Problems

**Symptoms**:
- Files fail to upload
- Upload progress stucks
- Invalid file type errors for valid files

**Diagnosis Steps**:
1. Check file size and format
2. Verify upload endpoint is accessible
3. Check browser file API support
4. Test with different file types

**Common Causes**:
- File size exceeds limits
- MIME type detection issues
- Network connectivity problems
- Browser compatibility issues

### Issue 6: Authentication Problems

**Symptoms**:
- Token deduction fails
- User session expires during generation
- Permission denied errors

**Diagnosis Steps**:
1. Check user authentication status
2. Verify token balance
3. Check session validity
4. Test with different users

### Issue 7: AI Generation Quality Issues

**Symptoms**:
- Generated letters are low quality
- Missing information extraction
- Incorrect formatting or language

**Diagnosis Steps**:
1. Check input data quality
2. Verify AI prompt configuration
3. Test with different resume/job combinations
4. Check AI model performance

**Troubleshooting Commands**:

```bash
# Check edge function status
supabase functions list

# View edge function logs
supabase functions logs generate-letter --follow

# Test edge function locally
supabase functions serve generate-letter

# Check database connections
supabase db ping

# View recent database changes
supabase db logs
```

**Environment Variable Verification**:
```bash
# Verify all required variables are set
echo "SUPABASE_URL: ${NEXT_PUBLIC_SUPABASE_URL:0:30}..."
echo "SUPABASE_ANON_KEY: ${NEXT_PUBLIC_SUPABASE_ANON_KEY:0:20}..."
echo "GOOGLE_AI_API_KEY: ${GOOGLE_AI_API_KEY:0:20}..."
```

## Performance Testing

### Load Testing

**Objective**: Verify system handles concurrent users appropriately.

**Steps**:
1. Use tools like Artillery or k6 to simulate concurrent requests
2. Test with 10, 50, and 100 concurrent users
3. Monitor response times and error rates
4. Check database performance metrics

**Performance Targets**:
- Initial API response: < 200ms
- Edge function completion: < 60 seconds
- Realtime update latency: < 2 seconds
- Error rate: < 1% under normal load

### Memory Usage Testing

**Steps**:
1. Monitor browser memory usage during generation
2. Check for memory leaks in long sessions
3. Verify proper cleanup of subscriptions
4. Test with multiple generations in single session

**Expected Results**:
- Memory usage remains stable
- No significant memory leaks
- Subscriptions are properly cleaned up

### API Rate Limiting

**Steps**:
1. Send multiple rapid requests from same user
2. Test Google AI API rate limits
3. Verify appropriate throttling mechanisms

**Expected Results**:
- Rate limiting prevents abuse
- Users receive appropriate feedback when limits exceeded
- System remains stable under high load

## Testing Checklist

### Pre-Deployment Testing

**Environment Setup**:
- [ ] All environment variables configured
- [ ] Database migration applied successfully
- [ ] Edge function deployed and accessible
- [ ] Realtime subscriptions enabled
- [ ] Test data prepared

**Basic Functionality**:
- [ ] Complete happy path flow works
- [ ] All file formats supported
- [ ] Template selection works
- [ ] PDF generation works
- [ ] Authentication flow works

**Error Handling**:
- [ ] Invalid file formats rejected appropriately
- [ ] Network interruption handling
- [ ] Edge function failure handling
- [ ] AI service failure handling
- [ ] Insufficient token handling

**Database Integration**:
- [ ] Records created correctly
- [ ] Status transitions work
- [ ] Structured data populated properly
- [ ] Error messages stored appropriately

**Realtime Updates**:
- [ ] Subscriptions establish correctly
- [ ] Updates received promptly
- [ ] Connection resilience works
- [ ] Cleanup happens properly

### Post-Deployment Testing

**Production Environment**:
- [ ] All features work in production
- [ ] Performance meets requirements
- [ ] Error monitoring is active
- [ ] Logging is comprehensive

**User Experience**:
- [ ] UI is responsive and intuitive
- [ ] Error messages are helpful
- [ ] Loading states are clear
- [ ] Success flows are smooth

**Security**:
- [ ] Input sanitization works
- [ ] Authentication is enforced
- [ ] File upload security measures active
- [ ] No sensitive data exposure

### Regression Testing

**After Code Changes**:
- [ ] Core functionality still works
- [ ] New features don't break existing ones
- [ ] Performance hasn't degraded
- [ ] Error handling still works

**After Infrastructure Changes**:
- [ ] Database connections work
- [ ] Edge functions are accessible
- [ ] Realtime updates still function
- [ ] Environment variables are correct

## Conclusion

This testing guide provides comprehensive coverage for the letter generation system. Regular execution of these tests ensures system reliability, performance, and user satisfaction. 

### Key Testing Principles

1. **Test Early and Often**: Run tests during development, not just before deployment
2. **Test the Full Stack**: Verify frontend, backend, database, and external integrations
3. **Test Real Scenarios**: Use realistic data and user workflows
4. **Monitor Production**: Continue testing and monitoring in production environment
5. **Document Issues**: Keep detailed records of issues and resolutions

### Maintenance

This testing guide should be updated whenever:
- New features are added to the system
- System architecture changes
- New error scenarios are discovered
- Performance requirements change
- User feedback indicates testing gaps

For questions or issues with this testing guide, please refer to the main project documentation or contact the development team.