# Generate Letter Supabase Edge Function

This Supabase Edge Function generates structured application letters using AI based on resume and job posting data.

## Overview

The `generate-letter` function processes letter generation requests by:
1. Accepting letter generation parameters (resume input, job input, template ID, letter ID)
2. Updating letter status to 'processing' in the database
3. Generating structured letter data using Google's Gemini AI
4. Updating the letter with generated content and status 'done'
5. Handling errors appropriately and updating status to 'error' when needed

## Request Format

**Method:** `POST`
**Content-Type:** `application/json`

### Request Body

```json
{
  "letterId": "uuid-of-existing-letter",
  "resumeInput": {
    "file": {
      "buffer": "base64-encoded-file-content",
      "extractedText": "text-content-for-docx-files",
      "mimeType": "application/pdf|application/vnd.openxmlformats-officedocument.wordprocessingml.document|text/plain|image/png|image/jpeg|image/jpg"
    }
    // OR
    "manual": {
      "fullName": "Candidate Name",
      "professionalTitle": "Job Title",
      "professionalSummary": "Brief summary",
      "mostRecentJob": {
        "title": "Job Title",
        "company": "Company Name",
        "achievements": "Key achievements"
      },
      "skills": "List of skills"
    }
  },
  "jobInput": {
    "description": "Job description text",
    // AND/OR
    "image": {
      "buffer": "base64-encoded-image",
      "mimeType": "image/png|image/jpeg|image/jpg"
    }
  },
  "templateId": "plain-text|classic-blue|professional-classic|minimalist-sidebar|etc"
}
```

### Required Parameters

- `letterId`: UUID of the existing letter record in the database
- `resumeInput`: Either file data or manual resume information
- `jobInput`: Either job description text or job posting image (or both)
- `templateId`: Template ID for letter formatting

## Response Format

### Success Response

```json
{
  "success": true,
  "letterId": "uuid-of-letter",
  "message": "Letter generated successfully",
  "structuredData": {
    "metadata": {
      "generatedAt": "2024-01-01T12:00:00.000Z",
      "lastModified": "2024-01-01T12:00:00.000Z",
      "templateId": "plain-text",
      "language": "id"
    },
    "header": {
      "date": "1 Januari 2024"
    },
    "subject": {
      "prefix": "Perihal: Lamaran Pekerjaan sebagai",
      "position": "Software Engineer"
    },
    "recipient": {
      "salutation": "Yth.",
      "title": "Bapak/Ibu Bagian Sumber Daya Manusia",
      "company": "PT. Example Company",
      "address": ["Jl. Example Street No. 123", "Jakarta 12345"]
    },
    "body": {
      "opening": "Dengan hormat,",
      "paragraphs": [
        "First paragraph content...",
        "Second paragraph content...",
        "Third paragraph content..."
      ],
      "closing": "Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih."
    },
    "signature": {
      "farewell": "Hormat saya,",
      "name": "Candidate Name"
    }
  }
}
```

### Error Response

```json
{
  "success": false,
  "error": "Error message description",
  "errorType": "ErrorType",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Database Updates

The function updates the `letters` table with the following fields:

- `status`: Updated to 'processing' → 'done' or 'error'
- `structured_data`: JSON containing the generated structured letter data
- `data`: Metadata including generation parameters and timestamps
- `error_message`: Error description (only when status is 'error')
- `updated_at`: Timestamp of the last update

## Supported File Formats

### Resume Files
- **PDF**: `application/pdf`
- **DOCX**: `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- **TXT**: `text/plain`
- **Images**: `image/png`, `image/jpeg`, `image/jpg`

### Job Posting Images
- **Images**: `image/png`, `image/jpeg`, `image/jpg`

## Environment Variables

The function requires the following environment variables:

- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key for database access
- `GOOGLE_AI_API_KEY`: Google AI API key for Gemini model access

## Error Handling

The function handles various error scenarios:

1. **Missing Environment Variables**: Returns 500 error with details
2. **Invalid Request Body**: Returns 500 error with parsing details
3. **Missing Required Parameters**: Returns 500 error with validation details
4. **Unsupported File Formats**: Returns 500 error with format requirements
5. **AI Generation Failures**: Updates letter status to 'error' and returns 500
6. **Database Update Failures**: Returns 500 error with database error details

## Usage Example

```javascript
const response = await fetch('/functions/v1/generate-letter', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${supabaseAnonKey}`
  },
  body: JSON.stringify({
    letterId: 'letter-uuid-here',
    resumeInput: {
      file: {
        buffer: 'base64-encoded-pdf-content',
        mimeType: 'application/pdf'
      }
    },
    jobInput: {
      description: 'Software Engineer position at Example Company...'
    },
    templateId: 'professional-classic'
  })
});

const result = await response.json();
console.log(result);
```

## Development Notes

- The function uses Google's Gemini 2.5 Flash model for AI generation
- Generated letters are in Indonesian by default
- The AI is prompted to create concise, professional letters (1300-1600 characters)
- Fallback structured data is provided if AI parsing fails
- All database operations use the service role key for full access
- CORS headers are included for browser compatibility

## Dependencies

- Deno Standard Library HTTP Server
- Supabase JavaScript Client
- Google Generative AI Client