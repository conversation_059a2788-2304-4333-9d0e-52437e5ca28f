-- Create letter_status enum type (same as resume_status)
CREATE TYPE "public"."letter_status" AS ENUM (
    'processing',
    'done',
    'error'
);

ALTER TYPE "public"."letter_status" OWNER TO "postgres";

-- Add status column to letters table
ALTER TABLE "public"."letters" 
ADD COLUMN "status" "public"."letter_status" DEFAULT 'processing'::"public"."letter_status";

-- Add error_message column for error tracking
ALTER TABLE "public"."letters" 
ADD COLUMN "error_message" "text";

-- Add structured_data JSONB column for storing the letter's structured data
ALTER TABLE "public"."letters" 
ADD COLUMN "structured_data" "jsonb";

-- Add data JSONB column for storing metadata like job info, resume info, etc.
ALTER TABLE "public"."letters" 
ADD COLUMN "data" "jsonb";

-- Create indexes for better performance
CREATE INDEX "idx_letters_status" ON "public"."letters" USING "btree" ("status");
CREATE INDEX "idx_letters_structured_data" ON "public"."letters" USING "gin" ("structured_data");
CREATE INDEX "idx_letters_data" ON "public"."letters" USING "gin" ("data");
CREATE INDEX "idx_letters_created_at" ON "public"."letters" USING "btree" ("created_at" DESC);
CREATE INDEX "idx_letters_user_id_status" ON "public"."letters" USING "btree" ("user_id", "status");

-- Add comment for documentation
COMMENT ON COLUMN "public"."letters"."status" IS 'Status of letter generation: processing, done, or error';
COMMENT ON COLUMN "public"."letters"."error_message" IS 'Error message when status is error';
COMMENT ON COLUMN "public"."letters"."structured_data" IS 'Structured data extracted from the letter content';
COMMENT ON COLUMN "public"."letters"."data" IS 'Metadata including job info, resume info, and other generation parameters';