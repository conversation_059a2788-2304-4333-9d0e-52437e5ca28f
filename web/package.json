{"name": "gigsta-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:letters": "jest --testPathPattern=__tests__.*letter", "test:letters:watch": "jest --testPathPattern=__tests__.*letter --watch"}, "dependencies": {"@google/genai": "^0.12.0", "@google/generative-ai": "^0.24.1", "@lottiefiles/dotlottie-react": "^0.13.5", "@netlify/edge-functions": "^2.14.5", "@next/third-parties": "^15.3.2", "@sparticuz/chromium-min": "^137.0.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "autoprefixer": "^10.4.16", "docx": "^9.5.0", "framer-motion": "^12.23.9", "handlebars": "^4.7.8", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "mammoth": "^1.9.0", "midtrans-client": "^1.4.3", "mixpanel": "^0.18.1", "mixpanel-browser": "^2.64.0", "next": "^15.3.1", "openai": "^4.103.0", "postcss": "^8.4.32", "puppeteer": "^24.10.0", "puppeteer-html-pdf": "^4.0.8", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.49.2", "react-icons": "^5.5.0", "react-pdf": "^10.0.1", "rollbar": "^2.26.4", "swr": "^2.3.3", "tailwindcss": "^3.3.0", "together-ai": "^0.16.0", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/jest": "^29.5.12", "@types/handlebars": "^4.1.0", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "handlebars-loader": "^1.7.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.9.2", "ts-jest": "^29.1.2", "typescript": "^5.3.3"}}